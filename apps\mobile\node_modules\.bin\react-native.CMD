@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native@0.79.4_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0\node_modules\react-native\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native@0.79.4_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native@0.79.4_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0\node_modules\react-native\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native@0.79.4_@babel+core@7.27.4_@types+react@19.0.14_react@19.0.0\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\react-native\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\react-native\cli.js" %*
)
