#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native-vector-icons@10.2.0\node_modules\react-native-vector-icons\bin\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native-vector-icons@10.2.0\node_modules\react-native-vector-icons\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\react-native-vector-icons@10.2.0\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/bin/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir//bin/sh$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir//bin/sh$exe"  "$basedir/../react-native-vector-icons/bin/fa6-upgrade.sh" $args
  } else {
    & "$basedir//bin/sh$exe"  "$basedir/../react-native-vector-icons/bin/fa6-upgrade.sh" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "/bin/sh$exe"  "$basedir/../react-native-vector-icons/bin/fa6-upgrade.sh" $args
  } else {
    & "/bin/sh$exe"  "$basedir/../react-native-vector-icons/bin/fa6-upgrade.sh" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
