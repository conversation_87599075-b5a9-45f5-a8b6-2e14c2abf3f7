#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/bin/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/bin/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules/react-native-vector-icons/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/react-native-vector-icons@10.2.0/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir//bin/sh" ]; then
  exec "$basedir//bin/sh"  "$basedir/../react-native-vector-icons/bin/fa5-upgrade.sh" "$@"
else
  exec /bin/sh  "$basedir/../react-native-vector-icons/bin/fa5-upgrade.sh" "$@"
fi
