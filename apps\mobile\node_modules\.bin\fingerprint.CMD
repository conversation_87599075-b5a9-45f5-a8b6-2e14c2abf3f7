@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules\expo\bin\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules\expo\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules\expo\bin\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules\expo\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0\node_modules;C:\Users\<USER>\Desktop\tap2go\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\expo\bin\fingerprint" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\expo\bin\fingerprint" %*
)
