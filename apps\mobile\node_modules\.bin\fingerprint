#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules/expo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules/expo/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules/expo/bin/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules/expo/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/expo@53.0.12_@babel+core@7.27.4_@expo+metro-runtime@5.0.4_react-native@0.79.4_react@19.0.0/node_modules:/mnt/c/Users/<USER>/Desktop/tap2go/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../expo/bin/fingerprint" "$@"
else
  exec node  "$basedir/../expo/bin/fingerprint" "$@"
fi
